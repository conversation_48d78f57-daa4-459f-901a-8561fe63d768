# Finalize Business Configuration for AMI
# This script performs final configuration steps for the business-ready AMI

Write-Host "Finalizing business configuration for AMI..." -ForegroundColor Green

try {
    # Create final validation and configuration
    Write-Host "Performing final business AMI validation..." -ForegroundColor Cyan
    
    $validationErrors = @()
    
    # Check if .NET Framework 4.8 is installed
    $net48Check = Get-ItemProperty "HKLM:SOFTWARE\Microsoft\NET Framework Setup\NDP\v4\Full\" -Name Release -ErrorAction SilentlyContinue
    if (!$net48Check -or $net48Check.Release -lt 528040) {
        $validationErrors += ".NET Framework 4.8 is not installed"
    } else {
        Write-Host "✓ .NET Framework 4.8 is installed (Release: $($net48Check.Release))" -ForegroundColor Green
    }
    
    # Check BGInfo installation
    $bginfoExe = "C:\Program Files\BGInfo\Bginfo.exe"
    if (!(Test-Path $bginfoExe)) {
        $validationErrors += "BGInfo is not installed"
    } else {
        Write-Host "✓ BGInfo is installed" -ForegroundColor Green
    }
    
    # Check RSAT tools
    $rsatFeatures = @("RSAT-AD-PowerShell", "RSAT-ADDS-Tools")
    foreach ($feature in $rsatFeatures) {
        $featureState = Get-WindowsFeature -Name $feature -ErrorAction SilentlyContinue
        if (!$featureState -or $featureState.InstallState -ne "Installed") {
            $validationErrors += "RSAT feature not installed: $feature"
        } else {
            Write-Host "✓ RSAT feature installed: $feature" -ForegroundColor Green
        }
    }
    
    # Check domain join scripts
    $requiredScripts = @(
        "C:\Scripts\DomainJoin\Join-BusinessDomain.ps1",
        "C:\Scripts\Process-UserData.ps1"
    )
    
    foreach ($script in $requiredScripts) {
        if (!(Test-Path $script)) {
            $validationErrors += "Required script not found: $script"
        } else {
            Write-Host "✓ Script found: $script" -ForegroundColor Green
        }
    }
    
    # Check business configuration directory
    $businessConfigDir = "C:\Scripts\BusinessConfigs"
    if (!(Test-Path $businessConfigDir)) {
        $validationErrors += "Business configuration directory not found: $businessConfigDir"
    } else {
        Write-Host "✓ Business configuration directory exists: $businessConfigDir" -ForegroundColor Green
    }
    
    # Report validation results
    if ($validationErrors.Count -eq 0) {
        Write-Host "`n✓ All validation checks passed!" -ForegroundColor Green
    } else {
        Write-Host "`n✗ Validation errors found:" -ForegroundColor Red
        foreach ($errors in $validationErrors) {
            Write-Host "  - $errors" -ForegroundColor Red
        }
        throw "AMI validation failed"
    }
    
    # Create AMI information file
    Write-Host "`nCreating AMI information file..." -ForegroundColor Cyan
    
    $amiInfo = @{
        BuildDate = (Get-Date).ToString("yyyy-MM-dd HH:mm:ss")
        BuildTool = "Packer"
        OSVersion = (Get-ComputerInfo).WindowsProductName
        Components = @{
            "DotNetFramework48" = $true
            "BGInfo" = $true
            "RSATTools" = $true
            "BusinessDomainJoin" = $true
        }
        Features = @{
            "AutomaticDomainJoin" = $true
            "BusinessConfiguration" = $true
            "MultiBusinessSupport" = $true
            "RoleBasedDeployment" = $true
        }
        Scripts = @{
            "DomainJoin" = "C:\Scripts\DomainJoin\Join-BusinessDomain.ps1"
            "UserDataProcessing" = "C:\Scripts\Process-UserData.ps1"
        }
        Directories = @{
            "BusinessConfigs" = "C:\Scripts\BusinessConfigs"
            "DomainJoin" = "C:\Scripts\DomainJoin"
        }
        Usage = @{
            "AutomaticDomainJoin" = "Set SERVER_ROLE, DOMAIN_USER, DOMAIN_PASSWORD environment variables and provide business config in user data"
            "ManualDomainJoin" = "Copy business config to C:\Scripts\BusinessConfigs\business-config.json and run Join-BusinessDomain.ps1"
        }
    }
    
    $amiInfo | ConvertTo-Json -Depth 10 | Out-File -FilePath "C:\Scripts\AMI-Info.json" -Encoding UTF8
    Write-Host "✓ AMI information file created: C:\Scripts\AMI-Info.json" -ForegroundColor Green
    
    # Update registry with final status
    Write-Host "Updating registry with final build status..." -ForegroundColor Cyan
    
    $regPath = "HKLM:\SOFTWARE\Company\BusinessDomainJoin"
    Set-ItemProperty -Path $regPath -Name "BuildCompleted" -Value (Get-Date).ToString()
    Set-ItemProperty -Path $regPath -Name "BuildStatus" -Value "Success"
    Set-ItemProperty -Path $regPath -Name "ValidationStatus" -Value "Passed"
    
    Write-Host "✓ Registry updated with build status" -ForegroundColor Green
    
    # Configure startup script for first boot
    Write-Host "Configuring startup script for first boot..." -ForegroundColor Cyan
    
    $startupScript = @'
# First Boot Configuration Script
# This script runs on first boot to process user data and configure the instance

$logFile = "C:\Scripts\first-boot.log"

function Write-Log {
    param([string]$Message)
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logEntry = "[$timestamp] $Message"
    Write-Host $logEntry
    $logEntry | Out-File -FilePath $logFile -Append -Encoding UTF8
}

try {
    Write-Log "Starting first boot configuration"
    
    # Process user data if available
    if (Test-Path "C:\Scripts\Process-UserData.ps1") {
        Write-Log "Processing user data for business configuration"
        & "C:\Scripts\Process-UserData.ps1"
    }
    
    Write-Log "First boot configuration completed"
    
} catch {
    Write-Log "First boot configuration failed: $($_.Exception.Message)"
}
'@
    
    $startupScript | Out-File -FilePath "C:\Scripts\FirstBoot.ps1" -Encoding UTF8
    
    # Create scheduled task for first boot (will be removed after first run)
    $taskName = "BusinessAMI-FirstBoot"
    $action = New-ScheduledTaskAction -Execute "PowerShell.exe" -Argument "-ExecutionPolicy Bypass -File C:\Scripts\FirstBoot.ps1"
    $trigger = New-ScheduledTaskTrigger -AtStartup
    $principal = New-ScheduledTaskPrincipal -UserId "SYSTEM" -LogonType ServiceAccount -RunLevel Highest
    $settings = New-ScheduledTaskSettingsSet -AllowStartIfOnBatteries -DontStopIfGoingOnBatteries -StartWhenAvailable
    
    Register-ScheduledTask -TaskName $taskName -Action $action -Trigger $trigger -Principal $principal -Settings $settings -Description "Business AMI first boot configuration"
    
    Write-Host "✓ First boot configuration script created" -ForegroundColor Green
    
    # Display final summary
    Write-Host "`n" + "="*60 -ForegroundColor Yellow
    Write-Host "BUSINESS AMI BUILD COMPLETED SUCCESSFULLY" -ForegroundColor Green
    Write-Host "="*60 -ForegroundColor Yellow
    
    Write-Host "`nAMI Features:" -ForegroundColor Yellow
    Write-Host "  ✓ Windows Server 2022 Base" -ForegroundColor White
    Write-Host "  ✓ .NET Framework 4.8" -ForegroundColor White
    Write-Host "  ✓ BGInfo with business configuration" -ForegroundColor White
    Write-Host "  ✓ RSAT tools for domain operations" -ForegroundColor White
    Write-Host "  ✓ Automatic business-specific domain join" -ForegroundColor White
    Write-Host "  ✓ Multi-business configuration support" -ForegroundColor White
    Write-Host "  ✓ Role-based server deployment" -ForegroundColor White
    
    Write-Host "`nUsage:" -ForegroundColor Yellow
    Write-Host "  1. Launch EC2 instance from this AMI" -ForegroundColor White
    Write-Host "  2. Provide business configuration in user data" -ForegroundColor White
    Write-Host "  3. Set environment variables for domain credentials" -ForegroundColor White
    Write-Host "  4. Instance will automatically join correct domain OU" -ForegroundColor White
    
    Write-Host "`nKey Files:" -ForegroundColor Yellow
    Write-Host "  - C:\Scripts\AMI-Info.json - AMI build information" -ForegroundColor White
    Write-Host "  - C:\Scripts\README-DomainJoin.txt - Usage instructions" -ForegroundColor White
    Write-Host "  - C:\Scripts\DomainJoin\Join-BusinessDomain.ps1 - Domain join script" -ForegroundColor White
    
    Write-Host "`nBuild completed on: $(Get-Date)" -ForegroundColor Cyan
    Write-Host "="*60 -ForegroundColor Yellow
    
} catch {
    Write-Error "Failed to finalize business configuration: $($_.Exception.Message)"
    Write-Error "Stack Trace: $($_.Exception.StackTrace)"
    exit 1
}
