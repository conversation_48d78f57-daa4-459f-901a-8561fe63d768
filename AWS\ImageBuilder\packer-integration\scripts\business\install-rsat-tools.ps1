# Install RSAT Tools for Business Domain Operations
# This script installs Remote Server Administration Tools needed for domain joining

Write-Host "Installing RSAT tools for business domain operations..." -ForegroundColor Green

try {
    # Install RSAT tools for domain management
    Write-Host "Installing Remote Server Administration Tools..." -ForegroundColor Cyan
    
    $features = @(
        "RSAT-AD-PowerShell",
        "RSAT-ADDS-Tools", 
        "RSAT-DNS-Server",
        "RSAT-DFS-Mgmt-Con",
        "RSAT-File-Services",
        "RSAT-NPS",
        "RSAT-RemoteAccess",
        "RSAT-ADCS",
        "RSAT-ADCS-Mgmt"
    )
    
    $installedFeatures = @()
    $failedFeatures = @()
    
    foreach ($feature in $features) {
        Write-Host "Installing feature: $feature" -ForegroundColor Cyan
        
        try {
            $featureState = Get-WindowsFeature -Name $feature -ErrorAction SilentlyContinue
            
            if ($featureState) {
                if ($featureState.InstallState -ne "Installed") {
                    $result = Install-WindowsFeature -Name $feature -IncludeManagementTools
                    if ($result.Success) {
                        Write-Host "✓ Successfully installed: $feature" -ForegroundColor Green
                        $installedFeatures += $feature
                    } else {
                        Write-Warning "Failed to install: $feature - $($result.ExitCode)"
                        $failedFeatures += $feature
                    }
                } else {
                    Write-Host "✓ Already installed: $feature" -ForegroundColor Yellow
                    $installedFeatures += $feature
                }
            } else {
                Write-Warning "Feature not available: $feature"
                $failedFeatures += $feature
            }
        } catch {
            Write-Warning "Error installing $feature : $($_.Exception.Message)"
            $failedFeatures += $feature
        }
    }
    
    # Summary
    Write-Host "`nRSAT Installation Summary:" -ForegroundColor Yellow
    Write-Host "Successfully installed/verified: $($installedFeatures.Count) features" -ForegroundColor Green
    if ($installedFeatures.Count -gt 0) {
        $installedFeatures | ForEach-Object { Write-Host "  ✓ $_" -ForegroundColor Green }
    }
    
    if ($failedFeatures.Count -gt 0) {
        Write-Host "Failed to install: $($failedFeatures.Count) features" -ForegroundColor Red
        $failedFeatures | ForEach-Object { Write-Host "  ✗ $_" -ForegroundColor Red }
    }
    
    # Verify Active Directory PowerShell module
    Write-Host "`nVerifying Active Directory PowerShell module..." -ForegroundColor Cyan
    try {
        Import-Module ActiveDirectory -ErrorAction Stop
        Write-Host "✓ Active Directory PowerShell module is available" -ForegroundColor Green
        
        # Get module information
        $adModule = Get-Module ActiveDirectory
        Write-Host "  Version: $($adModule.Version)" -ForegroundColor Cyan
        Write-Host "  Path: $($adModule.Path)" -ForegroundColor Cyan
    } catch {
        Write-Warning "Active Directory PowerShell module not available: $($_.Exception.Message)"
    }
    
    # Configure Windows Time Service for domain environment
    Write-Host "`nConfiguring Windows Time Service for domain synchronization..." -ForegroundColor Cyan
    try {
        # Set time service to manual peer list initially (will be updated for domain)
        w32tm /config /manualpeerlist:"time.windows.com,0x1" /syncfromflags:manual /reliable:yes /update
        
        # Restart time service
        Restart-Service w32time -Force
        
        # Force time sync
        w32tm /resync /force
        
        Write-Host "✓ Windows Time Service configured successfully" -ForegroundColor Green
        
        # Display current time configuration
        Write-Host "Current time configuration:" -ForegroundColor Cyan
        w32tm /query /configuration | Select-String "NtpServer|Type"
        
    } catch {
        Write-Warning "Time service configuration had issues: $($_.Exception.Message)"
        # Don't fail the build for time service issues
    }
    
    # Configure network settings for domain environment
    Write-Host "`nConfiguring network settings for domain environment..." -ForegroundColor Cyan
    try {
        # Enable NetBIOS over TCP/IP for domain compatibility
        Write-Host "Configuring NetBIOS settings..." -ForegroundColor Cyan
        
        # Get all network adapters
        $adapters = Get-NetAdapter | Where-Object { $_.Status -eq "Up" }
        
        foreach ($adapter in $adapters) {
            Write-Host "Configuring adapter: $($adapter.Name)" -ForegroundColor Cyan
            
            # Enable NetBIOS over TCP/IP
            $regPath = "HKLM:\SYSTEM\CurrentControlSet\Services\NetBT\Parameters\Interfaces"
            $adapterGuid = $adapter.InterfaceGuid
            $interfacePath = "$regPath\Tcpip_$adapterGuid"
            
            if (Test-Path $interfacePath) {
                # 0 = Use NetBIOS setting from DHCP server
                # 1 = Enable NetBIOS over TCP/IP
                # 2 = Disable NetBIOS over TCP/IP
                Set-ItemProperty -Path $interfacePath -Name "NetbiosOptions" -Value 1 -ErrorAction SilentlyContinue
                Write-Host "✓ NetBIOS enabled for adapter: $($adapter.Name)" -ForegroundColor Green
            }
        }
        
        # Configure DNS client settings
        Write-Host "Configuring DNS client settings..." -ForegroundColor Cyan
        Set-DnsClientGlobalSetting -SuffixSearchList @() -ErrorAction SilentlyContinue
        
        Write-Host "✓ Network settings configured for domain environment" -ForegroundColor Green
        
    } catch {
        Write-Warning "Network configuration had issues: $($_.Exception.Message)"
        # Don't fail the build for network configuration issues
    }
    
    # Configure firewall for domain operations
    Write-Host "`nConfiguring Windows Firewall for domain environment..." -ForegroundColor Cyan
    try {
        # Enable firewall rules commonly needed for domain operations
        $domainRules = @(
            "Core Networking - DNS (UDP-Out)",
            "Core Networking - DNS (UDP-In)", 
            "Windows Time",
            "File and Printer Sharing (Echo Request - ICMPv4-In)",
            "File and Printer Sharing (Echo Request - ICMPv6-In)"
        )
        
        foreach ($rule in $domainRules) {
            try {
                Enable-NetFirewallRule -DisplayName $rule -ErrorAction SilentlyContinue
                Write-Host "✓ Enabled firewall rule: $rule" -ForegroundColor Green
            } catch {
                Write-Host "Rule not found or already enabled: $rule" -ForegroundColor Yellow
            }
        }
        
        # Create custom rules for domain communication
        Write-Host "Creating custom domain communication rules..." -ForegroundColor Cyan
        
        # LDAP
        New-NetFirewallRule -DisplayName "Domain LDAP (TCP-389)" -Direction Outbound -Protocol TCP -LocalPort 389 -Action Allow -ErrorAction SilentlyContinue
        New-NetFirewallRule -DisplayName "Domain LDAP SSL (TCP-636)" -Direction Outbound -Protocol TCP -LocalPort 636 -Action Allow -ErrorAction SilentlyContinue
        
        # Kerberos
        New-NetFirewallRule -DisplayName "Domain Kerberos (TCP-88)" -Direction Outbound -Protocol TCP -LocalPort 88 -Action Allow -ErrorAction SilentlyContinue
        New-NetFirewallRule -DisplayName "Domain Kerberos (UDP-88)" -Direction Outbound -Protocol UDP -LocalPort 88 -Action Allow -ErrorAction SilentlyContinue
        
        # DNS
        New-NetFirewallRule -DisplayName "Domain DNS (UDP-53)" -Direction Outbound -Protocol UDP -LocalPort 53 -Action Allow -ErrorAction SilentlyContinue
        
        Write-Host "✓ Windows Firewall configured for domain environment" -ForegroundColor Green
        
    } catch {
        Write-Warning "Firewall configuration had issues: $($_.Exception.Message)"
        # Don't fail the build for firewall issues
    }
    
    Write-Host "`nRSAT tools installation and domain preparation completed successfully!" -ForegroundColor Green
    
} catch {
    Write-Error "Failed to install RSAT tools: $($_.Exception.Message)"
    Write-Error "Stack Trace: $($_.Exception.StackTrace)"
    exit 1
}
