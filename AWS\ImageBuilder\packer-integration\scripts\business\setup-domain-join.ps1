# Setup Domain Join Infrastructure for Business AMI
# This script prepares the AMI for business-specific domain joining using the JSON configurations

Write-Host "Setting up domain join infrastructure for business AMI..." -ForegroundColor Green

try {
    # Create scripts directory structure
    $scriptsPath = "C:\Scripts"
    $businessConfigsPath = "$scriptsPath\BusinessConfigs"
    $domainJoinPath = "$scriptsPath\DomainJoin"
    
    foreach ($path in @($scriptsPath, $businessConfigsPath, $domainJoinPath)) {
        if (!(Test-Path $path)) {
            New-Item -ItemType Directory -Path $path -Force | Out-Null
            Write-Host "Created directory: $path" -ForegroundColor Cyan
        }
    }
    
    # Create the main domain join script that reads business configuration
    Write-Host "Creating business-aware domain join script..." -ForegroundColor Cyan
    
    $domainJoinScript = @'
# Business-Aware Domain Join Script
# This script reads business configuration and joins the computer to the appropriate OU

param(
    [Parameter(Mandatory=$false)]
    [string]$BusinessConfigPath = "C:\Scripts\BusinessConfigs\business-config.json",
    
    [Parameter(Mandatory=$false)]
    [string]$ServerRole = "WebServer-2022",
    
    [Parameter(Mandatory=$false)]
    [string]$DomainUser,
    
    [Parameter(Mandatory=$false)]
    [string]$DomainPassword
)

$logFile = "C:\Scripts\DomainJoin\domain-join.log"

function Write-Log {
    param([string]$Message, [string]$Level = "INFO")
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logEntry = "[$timestamp] [$Level] $Message"
    Write-Host $logEntry
    $logEntry | Out-File -FilePath $logFile -Append -Encoding UTF8
}

try {
    Write-Log "Starting business-aware domain join process"
    
    # Read business configuration
    if (!(Test-Path $BusinessConfigPath)) {
        throw "Business configuration not found: $BusinessConfigPath"
    }
    
    $config = Get-Content $BusinessConfigPath -Raw | ConvertFrom-Json
    Write-Log "Loaded configuration for business: $($config.businessName)"
    
    # Get server role from environment variable or parameter
    if ($env:SERVER_ROLE) {
        $ServerRole = $env:SERVER_ROLE
    }
    
    Write-Log "Server role: $ServerRole"
    
    # Get target OU for this server role
    if ($config.serverOUs.$ServerRole) {
        $targetOU = $config.serverOUs.$ServerRole
        Write-Log "Target OU: $targetOU"
    } else {
        throw "Server role '$ServerRole' not found in configuration. Available roles: $($config.serverOUs.PSObject.Properties.Name -join ', ')"
    }
    
    # Get domain credentials from environment variables or parameters
    if (!$DomainUser -and $env:DOMAIN_USER) {
        $DomainUser = $env:DOMAIN_USER
    }
    if (!$DomainPassword -and $env:DOMAIN_PASSWORD) {
        $DomainPassword = $env:DOMAIN_PASSWORD
    }
    
    if (!$DomainUser -or !$DomainPassword) {
        throw "Domain credentials not provided. Set DOMAIN_USER and DOMAIN_PASSWORD environment variables or provide as parameters."
    }
    
    # Generate computer name with business prefix
    $computerName = $env:COMPUTERNAME
    if ($config.computerNamePrefix) {
        $suffix = $computerName.Substring($computerName.Length - 4)  # Keep last 4 chars
        $newComputerName = "$($config.computerNamePrefix)-$suffix"
        
        Write-Log "Renaming computer from $computerName to $newComputerName"
        Rename-Computer -NewName $newComputerName -Force
        $computerName = $newComputerName
    }
    
    # Test domain connectivity
    Write-Log "Testing connectivity to domain: $($config.domain)"
    $domainTest = Test-NetConnection -ComputerName $config.domain -Port 389 -InformationLevel Quiet
    if (!$domainTest) {
        throw "Cannot connect to domain $($config.domain) on port 389 (LDAP)"
    }
    Write-Log "✓ Domain connectivity verified"
    
    # Create credential object
    $securePassword = ConvertTo-SecureString $DomainPassword -AsPlainText -Force
    $credential = New-Object System.Management.Automation.PSCredential($DomainUser, $securePassword)
    
    # Join domain with specific OU
    Write-Log "Joining domain $($config.domain) in OU: $targetOU"
    Add-Computer -DomainName $config.domain -Credential $credential -OUPath $targetOU -Force
    
    Write-Log "✓ Successfully joined domain"
    Write-Log "Computer will restart to complete domain join"
    
    # Update BGInfo with business information
    Write-Log "Updating BGInfo with business information"
    $bginfoConfigPath = "C:\Program Files\BGInfo\bginfo.bgi"
    if (Test-Path $bginfoConfigPath) {
        $bginfoConfig = Get-Content $bginfoConfigPath -Raw
        $bginfoConfig = $bginfoConfig -replace "\[To be configured\]", $config.businessName
        $bginfoConfig = $bginfoConfig -replace "Server Role: \\cf4\\b\[To be configured\]", "Server Role: \\cf4\\b$ServerRole"
        $bginfoConfig | Out-File -FilePath $bginfoConfigPath -Encoding ASCII
        Write-Log "✓ BGInfo updated with business information"
    }
    
    # Schedule restart
    shutdown /r /t 60 /c "Restarting to complete domain join for $($config.businessName)"
    
} catch {
    Write-Log "Domain join failed: $($_.Exception.Message)" "ERROR"
    Write-Log "Stack trace: $($_.Exception.StackTrace)" "ERROR"
    exit 1
}
'@
    
    $domainJoinScript | Out-File -FilePath "$domainJoinPath\Join-BusinessDomain.ps1" -Encoding UTF8
    Write-Host "✓ Created business domain join script" -ForegroundColor Green
    
    # Create a PowerShell script to handle user data processing
    Write-Host "Creating user data processing script..." -ForegroundColor Cyan
    
    $userDataScript = @'
# Process EC2 User Data for Business Configuration
# This script processes user data to extract business configuration and trigger domain join

$logFile = "C:\Scripts\DomainJoin\userdata-processing.log"

function Write-Log {
    param([string]$Message, [string]$Level = "INFO")
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logEntry = "[$timestamp] [$Level] $Message"
    Write-Host $logEntry
    $logEntry | Out-File -FilePath $logFile -Append -Encoding UTF8
}

try {
    Write-Log "Starting user data processing for business configuration"
    
    # Check if business configuration was provided in user data
    $businessConfigPath = "C:\Scripts\BusinessConfigs\business-config.json"
    
    if (Test-Path $businessConfigPath) {
        Write-Log "Business configuration found, proceeding with domain join"
        
        # Get server role from environment variable
        $serverRole = $env:SERVER_ROLE
        if (!$serverRole) {
            $serverRole = "WebServer-2022"  # Default
            Write-Log "No server role specified, using default: $serverRole"
        }
        
        # Execute domain join
        & "C:\Scripts\DomainJoin\Join-BusinessDomain.ps1" -ServerRole $serverRole
        
    } else {
        Write-Log "No business configuration found, skipping automatic domain join"
        Write-Log "To join domain manually, place business config at: $businessConfigPath"
    }
    
} catch {
    Write-Log "User data processing failed: $($_.Exception.Message)" "ERROR"
}
'@
    
    $userDataScript | Out-File -FilePath "$scriptsPath\Process-UserData.ps1" -Encoding UTF8
    Write-Host "✓ Created user data processing script" -ForegroundColor Green
    
    # Create a batch file for easy execution
    Write-Host "Creating batch file wrapper..." -ForegroundColor Cyan
    
    $batchContent = @"
@echo off
echo Business Domain Join Utility
echo ============================
echo.
echo This utility will join the computer to the business domain using the provided configuration.
echo.
echo Usage: Join-BusinessDomain.ps1 -ServerRole "WebServer-2022" -DomainUser "domain\user" -DomainPassword "password"
echo.
echo Available in C:\Scripts\DomainJoin\
echo.
pause
"@
    
    $batchContent | Out-File -FilePath "$domainJoinPath\Join-BusinessDomain.bat" -Encoding ASCII
    Write-Host "✓ Created batch file wrapper" -ForegroundColor Green
    
    # Create registry entries for domain join information
    Write-Host "Creating registry configuration for domain join..." -ForegroundColor Cyan
    
    $regPath = "HKLM:\SOFTWARE\Company\BusinessDomainJoin"
    if (!(Test-Path $regPath)) {
        New-Item -Path $regPath -Force | Out-Null
        Write-Host "✓ Created registry path: $regPath" -ForegroundColor Green
    }
    
    # Set preparation timestamp
    Set-ItemProperty -Path $regPath -Name "PreparedDate" -Value (Get-Date).ToString()
    Set-ItemProperty -Path $regPath -Name "PreparedBy" -Value "Packer Business AMI Build"
    Set-ItemProperty -Path $regPath -Name "Status" -Value "Ready"
    Set-ItemProperty -Path $regPath -Name "Version" -Value "1.0"
    
    Write-Host "✓ Registry configuration completed" -ForegroundColor Green
    
    # Configure domain join related registry settings
    Write-Host "Setting domain-friendly registry configurations..." -ForegroundColor Cyan
    
    # Disable automatic logon (security best practice for domain environments)
    $winLogonPath = "HKLM:\SOFTWARE\Microsoft\Windows NT\CurrentVersion\Winlogon"
    Set-ItemProperty -Path $winLogonPath -Name "AutoAdminLogon" -Value "0" -ErrorAction SilentlyContinue
    
    # Configure LSA settings for domain authentication
    $lsaPath = "HKLM:\SYSTEM\CurrentControlSet\Control\Lsa"
    
    # Enable LM compatibility level for domain environments
    Set-ItemProperty -Path $lsaPath -Name "LmCompatibilityLevel" -Value 3 -ErrorAction SilentlyContinue
    
    Write-Host "✓ Registry settings configured for domain environment" -ForegroundColor Green
    
    # Create a template business configuration for reference
    Write-Host "Creating template business configuration..." -ForegroundColor Cyan
    
    $templateConfig = @{
        businessName = "TEMPLATE_BUSINESS"
        domain = "example.com"
        basePath = "OU=TEMPLATE,DC=example,DC=com"
        serverOUs = @{
            "WebServer-2022" = "OU=Web Server,OU=Server 2022,OU=Windows Servers,OU=Servers,OU=TEMPLATE,DC=example,DC=com"
            "SQLServer-2022" = "OU=SQL Server,OU=Server 2022,OU=Windows Servers,OU=Servers,OU=TEMPLATE,DC=example,DC=com"
        }
        computerNamePrefix = "TEMP"
    }
    
    $templateConfig | ConvertTo-Json -Depth 10 | Out-File -FilePath "$businessConfigsPath\template-business-config.json" -Encoding UTF8
    Write-Host "✓ Created template business configuration" -ForegroundColor Green
    
    # Create README file with instructions
    Write-Host "Creating README with usage instructions..." -ForegroundColor Cyan
    
    $readmeContent = @"
# Business Domain Join Setup

This AMI is configured for automatic business-specific domain joining.

## Files Created:
- C:\Scripts\DomainJoin\Join-BusinessDomain.ps1 - Main domain join script
- C:\Scripts\Process-UserData.ps1 - User data processing script
- C:\Scripts\BusinessConfigs\ - Directory for business configurations

## Usage:

### 1. Automatic via User Data:
Place your business configuration JSON in EC2 user data, and set environment variables:
- SERVER_ROLE=WebServer-2022
- DOMAIN_USER=domain\username
- DOMAIN_PASSWORD=password

### 2. Manual Domain Join:
Copy your business configuration to C:\Scripts\BusinessConfigs\business-config.json
Then run:
PowerShell.exe -ExecutionPolicy Bypass -File "C:\Scripts\DomainJoin\Join-BusinessDomain.ps1" -ServerRole "WebServer-2022" -DomainUser "domain\user" -DomainPassword "password"

## Business Configuration Format:
{
  "businessName": "Business1",
  "domain": "mud.internal.co.za",
  "serverOUs": {
    "WebServer-2022": "OU=Web Server,OU=Server 2022,OU=Windows Servers,OU=Servers,OU=Business1,OU=Businesses,DC=mud,DC=internal,DC=co,DC=za"
  },
  "computerNamePrefix": "BUS1"
}

Built with Packer on $(Get-Date)
"@
    
    $readmeContent | Out-File -FilePath "$scriptsPath\README-DomainJoin.txt" -Encoding UTF8
    Write-Host "✓ Created README with usage instructions" -ForegroundColor Green
    
    Write-Host "`nDomain join infrastructure setup completed successfully!" -ForegroundColor Green
    Write-Host "`nCreated files:" -ForegroundColor Yellow
    Write-Host "  - C:\Scripts\DomainJoin\Join-BusinessDomain.ps1" -ForegroundColor White
    Write-Host "  - C:\Scripts\Process-UserData.ps1" -ForegroundColor White
    Write-Host "  - C:\Scripts\BusinessConfigs\template-business-config.json" -ForegroundColor White
    Write-Host "  - C:\Scripts\README-DomainJoin.txt" -ForegroundColor White
    
} catch {
    Write-Error "Failed to setup domain join infrastructure: $($_.Exception.Message)"
    Write-Error "Stack Trace: $($_.Exception.StackTrace)"
    exit 1
}
