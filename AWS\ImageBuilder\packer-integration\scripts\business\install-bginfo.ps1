# Install and Configure BGInfo for Business AMI
# This script installs BGInfo with business-specific configuration

Write-Host "Installing and configuring BGInfo for business AMI..." -ForegroundColor Green

try {
    $installPath = "C:\Program Files\BGInfo"
    $configPath = "$installPath\bginfo.bgi"
    
    # Create installation directory
    if (!(Test-Path $installPath)) {
        Write-Host "Creating installation directory: $installPath" -ForegroundColor Cyan
        New-Item -ItemType Directory -Path $installPath -Force | Out-Null
    }
    
    # Create temp directory for download
    $tempDir = "C:\temp"
    if (!(Test-Path $tempDir)) {
        New-Item -ItemType Directory -Path $tempDir -Force | Out-Null
    }
    
    # Download BGInfo
    $downloadUrl = "https://download.sysinternals.com/files/BGInfo.zip"
    $zipPath = "$tempDir\BGInfo.zip"
    
    Write-Host "Downloading BGInfo from Sysinternals..." -ForegroundColor Cyan
    Invoke-WebRequest -Uri $downloadUrl -OutFile $zipPath -UseBasicParsing
    
    # Verify download
    if (Test-Path $zipPath) {
        $fileSize = (Get-Item $zipPath).Length
        Write-Host "Download completed successfully. File size: $fileSize bytes" -ForegroundColor Green
    } else {
        throw "Download failed - file not found"
    }
    
    # Extract BGInfo
    Write-Host "Extracting BGInfo to: $installPath" -ForegroundColor Cyan
    Add-Type -AssemblyName System.IO.Compression.FileSystem
    [System.IO.Compression.ZipFile]::ExtractToDirectory($zipPath, $installPath)
    
    # Verify extraction
    $bginfoExe = "$installPath\Bginfo.exe"
    if (Test-Path $bginfoExe) {
        $version = (Get-Item $bginfoExe).VersionInfo.FileVersion
        Write-Host "BGInfo extracted successfully. Version: $version" -ForegroundColor Green
    } else {
        throw "BGInfo.exe not found after extraction"
    }
    
    # Create business-specific BGInfo configuration
    Write-Host "Creating business-specific BGInfo configuration..." -ForegroundColor Cyan
    
    $bgiConfig = @"
[BGInfo]
RTF={\rtf1\ansi\deff0{\fonttbl{\f0\fnil\fcharset0 Arial;}}{\colortbl ;\red255\green255\blue255;\red0\green0\blue0;\red255\green0\blue0;\red0\green128\blue0;}
\cf2\fs16\b Business Server Information\b0\fs14
\line Computer Name: \cf3\b<Computer Name>\cf2\b0
\line Business: \cf4\b[To be configured]\cf2\b0
\line Server Role: \cf4\b[To be configured]\cf2\b0
\line
\line \fs16\b AWS Instance Information\b0\fs14
\line Instance ID: \cf3\b<EC2 Instance ID>\cf2\b0
\line Instance Type: \cf3\b<EC2 Instance Type>\cf2\b0
\line Availability Zone: \cf3\b<EC2 Availability Zone>\cf2\b0
\line
\line \fs16\b Network Information\b0\fs14
\line IP Address: \cf3\b<IP Address>\cf2\b0
\line Subnet Mask: \cf3\b<Subnet Mask>\cf2\b0
\line Default Gateway: \cf3\b<Default Gateway>\cf2\b0
\line DNS Server: \cf3\b<DNS Server>\cf2\b0
\line
\line \fs16\b Domain Information\b0\fs14
\line Domain: \cf3\b<Domain>\cf2\b0
\line Logon Domain: \cf3\b<Logon Domain>\cf2\b0
\line Logon Server: \cf3\b<Logon Server>\cf2\b0
\line
\line \fs16\b System Information\b0\fs14
\line OS Version: \cf3\b<OS Version>\cf2\b0
\line System Type: \cf3\b<System Type>\cf2\b0
\line Processor: \cf3\b<Processor>\cf2\b0
\line Memory: \cf3\b<Memory>\cf2\b0
\line
\line \fs16\b Timing Information\b0\fs14
\line Boot Time: \cf3\b<Boot Time>\cf2\b0
\line Logon Time: \cf3\b<Logon Time>\cf2\b0
\line
\line \fs16\b Storage Information\b0\fs14
\line Free Space C:: \cf3\b<Free Space C:>\cf2\b0
\line Free Space D:: \cf3\b<Free Space D:>\cf2\b0
}
Position=0
TextWidth=420
TextHeight=0
Transparency=200
Timeout=0
"@
    
    # Save configuration
    $bgiConfig | Out-File -FilePath $configPath -Encoding ASCII
    Write-Host "BGInfo configuration saved to: $configPath" -ForegroundColor Green
    
    # Accept EULA in registry for current user and default user
    Write-Host "Accepting BGInfo EULA..." -ForegroundColor Cyan
    
    # Current user
    $registryPath = "HKCU:\Software\Sysinternals\BGInfo"
    if (!(Test-Path $registryPath)) {
        New-Item -Path $registryPath -Force | Out-Null
    }
    Set-ItemProperty -Path $registryPath -Name "EulaAccepted" -Value 1
    
    # Default user profile
    reg load HKU\DefaultUser "C:\Users\<USER>\NTUSER.DAT" 2>$null
    $defaultUserPath = "HKU:\DefaultUser\Software\Sysinternals\BGInfo"
    if (!(Test-Path $defaultUserPath)) {
        New-Item -Path $defaultUserPath -Force | Out-Null
    }
    Set-ItemProperty -Path $defaultUserPath -Name "EulaAccepted" -Value 1
    reg unload HKU\DefaultUser 2>$null
    
    Write-Host "EULA accepted for current and default users" -ForegroundColor Green
    
    # Create startup registry entry for all users
    Write-Host "Setting up BGInfo to run at startup..." -ForegroundColor Cyan
    $startupRegPath = "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\Run"
    $startupCommand = "`"$bginfoExe`" `"$configPath`" /timer:0 /nolicprompt"
    Set-ItemProperty -Path $startupRegPath -Name "BGInfo" -Value $startupCommand
    
    Write-Host "BGInfo startup entry created in registry" -ForegroundColor Green
    
    # Create a scheduled task as backup method
    $taskName = "BGInfo Startup"
    $taskDescription = "Display business system information on desktop using BGInfo"
    
    # Remove existing task if it exists
    try {
        Unregister-ScheduledTask -TaskName $taskName -Confirm:$false -ErrorAction SilentlyContinue
    } catch {}
    
    # Create new scheduled task
    $action = New-ScheduledTaskAction -Execute $bginfoExe -Argument "`"$configPath`" /timer:0 /nolicprompt"
    $trigger = New-ScheduledTaskTrigger -AtLogOn
    $principal = New-ScheduledTaskPrincipal -GroupId "Users" -RunLevel Limited
    $settings = New-ScheduledTaskSettingsSet -AllowStartIfOnBatteries -DontStopIfGoingOnBatteries -StartWhenAvailable
    
    Register-ScheduledTask -TaskName $taskName -Action $action -Trigger $trigger -Principal $principal -Settings $settings -Description $taskDescription
    
    Write-Host "BGInfo scheduled task created successfully" -ForegroundColor Green
    
    # Test run BGInfo
    Write-Host "Running BGInfo test..." -ForegroundColor Cyan
    $process = Start-Process -FilePath $bginfoExe -ArgumentList "`"$configPath`"", "/timer:0", "/nolicprompt" -Wait -PassThru
    
    if ($process.ExitCode -eq 0) {
        Write-Host "BGInfo test run completed successfully" -ForegroundColor Green
    } else {
        Write-Warning "BGInfo test run completed with exit code: $($process.ExitCode)"
    }
    
    # Cleanup
    Write-Host "Cleaning up installation files..." -ForegroundColor Cyan
    if (Test-Path $zipPath) {
        Remove-Item $zipPath -Force
        Write-Host "Removed: $zipPath" -ForegroundColor Green
    }
    
    Write-Host "BGInfo installation and configuration completed successfully!" -ForegroundColor Green
    Write-Host "BGInfo will automatically run at startup and display business system information on the desktop." -ForegroundColor Yellow
    
} catch {
    Write-Error "Failed to install BGInfo: $($_.Exception.Message)"
    Write-Error "Stack Trace: $($_.Exception.StackTrace)"
    exit 1
}
