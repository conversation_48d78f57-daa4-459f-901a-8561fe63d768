# Install .NET Framework 4.8 for Business AMI
# This script installs .NET Framework 4.8 with proper error handling and validation

Write-Host "Starting .NET Framework 4.8 installation for business AMI..." -ForegroundColor Green

try {
    # Check if .NET 4.8 is already installed
    Write-Host "Checking existing .NET Framework versions..."
    $dotNetVersions = Get-ChildItem "HKLM:SOFTWARE\Microsoft\NET Framework Setup\NDP" -Recurse |
        Get-ItemProperty -Name Version -EA 0 |
        Where-Object { $_.PSChildName -match '^(?!S)\p{L}' } |
        Select-Object PSChildName, Version
    
    Write-Host "Installed .NET Framework versions:"
    $dotNetVersions | Format-Table -AutoSize
    
    # Check if .NET 4.8 is already installed
    $net48Installed = Get-ItemProperty "HKLM:SOFTWARE\Microsoft\NET Framework Setup\NDP\v4\Full\" -Name Release -ErrorAction SilentlyContinue
    if ($net48Installed -and $net48Installed.Release -ge 528040) {
        Write-Host ".NET Framework 4.8 or later is already installed (Release: $($net48Installed.Release))" -ForegroundColor Yellow
        exit 0
    }
    
    Write-Host ".NET Framework 4.8 is not installed. Proceeding with installation..." -ForegroundColor Yellow
    
    # Create temp directory
    $tempDir = "C:\temp"
    if (!(Test-Path $tempDir)) {
        New-Item -ItemType Directory -Path $tempDir -Force | Out-Null
    }
    
    # Download .NET Framework 4.8
    $downloadUrl = "https://download.microsoft.com/download/7/D/E/7DE2B5F8-B4B8-4B0E-9F0E-9B3B5B5B5B5B/ndp48-web.exe"
    $downloadPath = "$tempDir\ndp48-web.exe"
    
    Write-Host "Downloading .NET Framework 4.8 installer..." -ForegroundColor Cyan
    Invoke-WebRequest -Uri $downloadUrl -OutFile $downloadPath -UseBasicParsing
    Write-Host "Download completed successfully" -ForegroundColor Green
    
    # Install .NET Framework 4.8
    Write-Host "Installing .NET Framework 4.8..." -ForegroundColor Cyan
    Write-Host "This may take several minutes..." -ForegroundColor Yellow
    
    $process = Start-Process -FilePath $downloadPath -ArgumentList "/quiet", "/norestart" -Wait -PassThru
    
    Write-Host "Installation process completed with exit code: $($process.ExitCode)" -ForegroundColor Cyan
    
    # Handle exit codes
    switch ($process.ExitCode) {
        0 { 
            Write-Host ".NET Framework 4.8 installed successfully" -ForegroundColor Green
        }
        1641 { 
            Write-Host ".NET Framework 4.8 installed successfully (reboot initiated)" -ForegroundColor Green
        }
        3010 { 
            Write-Host ".NET Framework 4.8 installed successfully (reboot required)" -ForegroundColor Green
            # Packer will handle the reboot
        }
        default { 
            Write-Error "Installation failed with exit code: $($process.ExitCode)"
            exit 1
        }
    }
    
    # Verify installation
    Write-Host "Verifying .NET Framework 4.8 installation..." -ForegroundColor Cyan
    Start-Sleep -Seconds 5  # Give registry time to update
    
    $net48Check = Get-ItemProperty "HKLM:SOFTWARE\Microsoft\NET Framework Setup\NDP\v4\Full\" -Name Release -ErrorAction SilentlyContinue
    
    if ($net48Check -and $net48Check.Release -ge 528040) {
        Write-Host "SUCCESS: .NET Framework 4.8 is installed (Release: $($net48Check.Release))" -ForegroundColor Green
        
        # Display all installed .NET versions for verification
        Write-Host "`nAll installed .NET Framework versions:" -ForegroundColor Cyan
        Get-ChildItem "HKLM:SOFTWARE\Microsoft\NET Framework Setup\NDP" -Recurse |
            Get-ItemProperty -Name Version -EA 0 |
            Where-Object { $_.PSChildName -match '^(?!S)\p{L}' } |
            Select-Object PSChildName, Version |
            Format-Table -AutoSize
    } else {
        Write-Error "FAILED: .NET Framework 4.8 installation could not be verified"
        exit 1
    }
    
    # Cleanup
    Write-Host "Cleaning up installation files..." -ForegroundColor Cyan
    if (Test-Path $downloadPath) {
        Remove-Item $downloadPath -Force
        Write-Host "Installer file removed" -ForegroundColor Green
    }
    
    Write-Host ".NET Framework 4.8 installation completed successfully!" -ForegroundColor Green
    
} catch {
    Write-Error "Failed to install .NET Framework 4.8: $($_.Exception.Message)"
    Write-Error "Stack Trace: $($_.Exception.StackTrace)"
    exit 1
}
