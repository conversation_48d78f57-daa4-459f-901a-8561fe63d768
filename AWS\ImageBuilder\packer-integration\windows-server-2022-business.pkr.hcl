# Windows Server 2022 Business-Specific AMI Template with Domain Join Support
# This template creates AMIs that can automatically join specific business domains and OUs

packer {
  required_plugins {
    amazon = {
      version = ">= 1.2.0"
      source  = "github.com/hashicorp/amazon"
    }
  }
}

# Variables
variable "aws_region" {
  type        = string
  description = "AWS region"
  default     = "us-east-1"
}

variable "aws_access_key" {
  type        = string
  description = "AWS access key"
  default     = ""
}

variable "aws_secret_key" {
  type        = string
  description = "AWS secret key"
  sensitive   = true
  default     = ""
}

variable "instance_type" {
  type        = string
  description = "EC2 instance type"
  default     = "t3.large"
}

variable "ami_name" {
  type        = string
  description = "AMI name"
  default     = "windows-server-2022-business-ready-{{timestamp}}"
}

variable "ami_description" {
  type        = string
  description = "AMI description"
  default     = "Windows Server 2022 Business-Ready with Domain Join Support"
}

variable "vpc_id" {
  type        = string
  description = "VPC ID for build instance"
  default     = ""
}

variable "subnet_id" {
  type        = string
  description = "Subnet ID for build instance"
  default     = ""
}

variable "security_group_id" {
  type        = string
  description = "Security group ID for build instance"
  default     = ""
}

variable "winrm_username" {
  type        = string
  description = "WinRM username"
  default     = "Administrator"
}

variable "business_configs_path" {
  type        = string
  description = "Path to business configuration files"
  default     = "../configs"
}

variable "include_dotnet48" {
  type        = bool
  description = "Include .NET Framework 4.8 installation"
  default     = true
}

variable "include_bginfo" {
  type        = bool
  description = "Include BGInfo installation and configuration"
  default     = true
}

variable "include_domain_join_prep" {
  type        = bool
  description = "Include domain join preparation"
  default     = true
}

# Data sources for latest Windows Server 2022 AMI
data "amazon-ami" "windows-server-2022" {
  filters = {
    name                = "Windows_Server-2022-English-Full-Base-*"
    root-device-type    = "ebs"
    virtualization-type = "hvm"
  }
  most_recent = true
  owners      = ["amazon"]
  region      = var.aws_region
}

# Source configuration
source "amazon-ebs" "windows-server-2022-business" {
  # AWS configuration
  access_key = var.aws_access_key
  secret_key = var.aws_secret_key
  region     = var.aws_region

  # Instance configuration
  source_ami_filter {
    filters = {
      name                = "Windows_Server-2022-English-Full-Base-*"
      root-device-type    = "ebs"
      virtualization-type = "hvm"
    }
    most_recent = true
    owners      = ["amazon"]
  }

  instance_type = var.instance_type
  
  # Network configuration
  vpc_id                      = var.vpc_id
  subnet_id                   = var.subnet_id
  security_group_id           = var.security_group_id
  associate_public_ip_address = true

  # AMI configuration
  ami_name        = var.ami_name
  ami_description = var.ami_description
  
  # Tags
  tags = {
    Name         = var.ami_name
    OS           = "Windows Server 2022"
    Environment  = "Business-Ready"
    CreatedBy    = "Packer"
    CreatedDate  = "{{timestamp}}"
    Components   = ".NET 4.8, BGInfo, Domain Join Ready, Business Config Support"
    Purpose      = "Multi-Business Domain Join"
  }

  # WinRM configuration
  communicator   = "winrm"
  winrm_username = var.winrm_username
  winrm_use_ssl  = true
  winrm_insecure = true
  winrm_timeout  = "30m"

  # User data for WinRM setup
  user_data_file = "scripts/setup/aws-userdata.ps1"
}

# Build configuration
build {
  sources = ["source.amazon-ebs.windows-server-2022-business"]

  # Wait for WinRM to be available
  provisioner "powershell" {
    inline = ["Write-Host 'WinRM is ready for business AMI build'"]
  }

  # Install Windows Updates
  provisioner "powershell" {
    script = "scripts/windows-updates.ps1"
  }

  # Install AWS tools and drivers
  provisioner "powershell" {
    script = "scripts/install-aws-tools.ps1"
  }

  # Configure Windows features and roles
  provisioner "powershell" {
    script = "scripts/configure-windows-features.ps1"
  }

  # Install .NET Framework 4.8 (conditional)
  provisioner "powershell" {
    only   = ["amazon-ebs.windows-server-2022-business"]
    script = "scripts/business/install-dotnet48.ps1"
    valid_exit_codes = [0, 3010]  # Allow reboot required exit code
  }

  # Install and configure BGInfo (conditional)
  provisioner "powershell" {
    only   = ["amazon-ebs.windows-server-2022-business"]
    script = "scripts/business/install-bginfo.ps1"
  }

  # Install RSAT tools for domain operations
  provisioner "powershell" {
    script = "scripts/business/install-rsat-tools.ps1"
  }

  # Copy business configuration templates
  provisioner "file" {
    sources     = ["${var.business_configs_path}/"]
    destination = "C:/Scripts/BusinessConfigs/"
  }

  # Setup domain join infrastructure
  provisioner "powershell" {
    script = "scripts/business/setup-domain-join.ps1"
  }

  # Install common software (from your existing script)
  provisioner "powershell" {
    script = "scripts/install-common-software.ps1"
  }

  # Security hardening
  provisioner "powershell" {
    script = "scripts/security-hardening.ps1"
  }

  # AWS-specific configuration
  provisioner "powershell" {
    script = "scripts/aws-specific-config.ps1"
  }

  # Business-specific final configuration
  provisioner "powershell" {
    script = "scripts/business/finalize-business-config.ps1"
  }

  # Final cleanup
  provisioner "powershell" {
    script = "scripts/cleanup.ps1"
  }

  # Restart to ensure all installations are complete
  provisioner "windows-restart" {
    restart_timeout = "15m"
  }

  # EC2Config/EC2Launch preparation
  provisioner "powershell" {
    inline = [
      "Write-Host 'Preparing EC2 instance for AMI creation...'",
      "C:/ProgramData/Amazon/EC2-Windows/Launch/Scripts/InitializeInstance.ps1 -Schedule",
      "C:/ProgramData/Amazon/EC2-Windows/Launch/Scripts/SysprepInstance.ps1 -NoShutdown"
    ]
  }

  # Post-processor to create manifest
  post-processor "manifest" {
    output = "manifests/windows-server-2022-business-manifest.json"
    strip_path = true
    custom_data = {
      build_time = "{{timestamp}}"
      build_user = "{{user `build_user`}}"
      source_ami = "{{.SourceAMI}}"
      components = ".NET 4.8, BGInfo, RSAT Tools, Business Domain Join Support"
    }
  }
}
