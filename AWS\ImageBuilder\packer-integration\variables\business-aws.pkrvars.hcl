# Business-Specific AWS Variables for Packer
# Update these values to match your AWS environment and business requirements

# AWS credentials and region
aws_region     = "us-east-1"  # Change to your preferred region
aws_access_key = ""  # Leave empty to use AWS CLI/IAM role
aws_secret_key = ""  # Leave empty to use AWS CLI/IAM role

# EC2 instance configuration
instance_type = "t3.large"  # Suitable for Windows builds with business components

# Network configuration (recommended for production)
vpc_id            = ""  # Your VPC ID for build instance
subnet_id         = ""  # Your subnet ID (private subnet with NAT Gateway recommended)
security_group_id = ""  # Your security group ID (allow outbound HTTPS, HTTP, DNS)

# AMI naming for business-ready images
ami_name        = "windows-server-2022-business-ready-{{timestamp}}"
ami_description = "Windows Server 2022 Business-Ready with .NET 4.8, BGInfo, RSAT, and Multi-Business Domain Join Support"

# WinRM configuration
winrm_username = "Administrator"

# Business configuration
business_configs_path = "../configs"  # Path to your business JSON configuration files

# Component toggles
include_dotnet48         = true   # Install .NET Framework 4.8
include_bginfo          = true   # Install and configure BGInfo
include_domain_join_prep = true   # Include domain join preparation

# Build user (for manifest)
build_user = "packer-automation"
