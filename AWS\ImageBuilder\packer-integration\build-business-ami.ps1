# Build Business-Ready AMI with Packer
# This script builds a Windows Server 2022 AMI with business-specific domain join capabilities

param(
    [Parameter(Mandatory=$false)]
    [string]$Region = "us-east-1",
    
    [Parameter(Mandatory=$false)]
    [string]$VpcId = "",
    
    [Parameter(Mandatory=$false)]
    [string]$SubnetId = "",
    
    [Parameter(Mandatory=$false)]
    [string]$SecurityGroupId = "",
    
    [Parameter(Mandatory=$false)]
    [string]$InstanceType = "t3.large",
    
    [Parameter(Mandatory=$false)]
    [string]$BusinessConfigsPath = "../configs",
    
    [Parameter(Mandatory=$false)]
    [switch]$ValidateOnly = $false,
    
    [Parameter(Mandatory=$false)]
    [switch]$Debug = $false
)

Write-Host "Building Business-Ready Windows Server 2022 AMI with Packer" -ForegroundColor Green
Write-Host "==========================================================" -ForegroundColor Green

# Function to check prerequisites
function Test-Prerequisites {
    Write-Host "Checking prerequisites..." -ForegroundColor Yellow
    
    $errors = @()
    
    # Check if Packer is installed
    try {
        $packerVersion = packer version
        Write-Host "✓ Packer is installed: $packerVersion" -ForegroundColor Green
    } catch {
        $errors += "Packer is not installed or not in PATH"
    }
    
    # Check if AWS CLI is configured
    try {
        $identity = aws sts get-caller-identity --output json | ConvertFrom-Json
        Write-Host "✓ AWS CLI configured for account: $($identity.Account)" -ForegroundColor Green
    } catch {
        $errors += "AWS CLI is not configured"
    }
    
    # Check if business configurations exist
    if (Test-Path $BusinessConfigsPath) {
        $configFiles = Get-ChildItem "$BusinessConfigsPath\*.json"
        if ($configFiles.Count -gt 0) {
            Write-Host "✓ Found $($configFiles.Count) business configuration files" -ForegroundColor Green
            foreach ($file in $configFiles) {
                Write-Host "  - $($file.Name)" -ForegroundColor Cyan
            }
        } else {
            $errors += "No business configuration JSON files found in $BusinessConfigsPath"
        }
    } else {
        $errors += "Business configurations directory not found: $BusinessConfigsPath"
    }
    
    # Check Packer template
    $packerTemplate = "windows-server-2022-business.pkr.hcl"
    if (!(Test-Path $packerTemplate)) {
        $errors += "Packer template not found: $packerTemplate"
    } else {
        Write-Host "✓ Packer template found: $packerTemplate" -ForegroundColor Green
    }
    
    # Check variables file
    $variablesFile = "variables/business-aws.pkrvars.hcl"
    if (!(Test-Path $variablesFile)) {
        $errors += "Variables file not found: $variablesFile"
    } else {
        Write-Host "✓ Variables file found: $variablesFile" -ForegroundColor Green
    }
    
    if ($errors.Count -gt 0) {
        Write-Host "`nPrerequisite errors:" -ForegroundColor Red
        foreach ($error in $errors) {
            Write-Host "  ✗ $error" -ForegroundColor Red
        }
        return $false
    }
    
    return $true
}

# Function to validate Packer template
function Test-PackerTemplate {
    Write-Host "Validating Packer template..." -ForegroundColor Yellow
    
    try {
        $validateArgs = @(
            "validate",
            "-var-file=variables/business-aws.pkrvars.hcl"
        )
        
        if ($VpcId) { $validateArgs += "-var=vpc_id=$VpcId" }
        if ($SubnetId) { $validateArgs += "-var=subnet_id=$SubnetId" }
        if ($SecurityGroupId) { $validateArgs += "-var=security_group_id=$SecurityGroupId" }
        if ($Region) { $validateArgs += "-var=aws_region=$Region" }
        if ($InstanceType) { $validateArgs += "-var=instance_type=$InstanceType" }
        if ($BusinessConfigsPath) { $validateArgs += "-var=business_configs_path=$BusinessConfigsPath" }
        
        $validateArgs += "windows-server-2022-business.pkr.hcl"
        
        Write-Host "Running: packer $($validateArgs -join ' ')" -ForegroundColor Cyan
        
        & packer @validateArgs
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✓ Packer template validation successful" -ForegroundColor Green
            return $true
        } else {
            Write-Host "✗ Packer template validation failed" -ForegroundColor Red
            return $false
        }
        
    } catch {
        Write-Error "Failed to validate Packer template: $($_.Exception.Message)"
        return $false
    }
}

# Function to build AMI
function Start-PackerBuild {
    Write-Host "Starting Packer build..." -ForegroundColor Yellow
    
    try {
        # Create manifests directory if it doesn't exist
        if (!(Test-Path "manifests")) {
            New-Item -ItemType Directory -Path "manifests" -Force | Out-Null
        }
        
        $buildArgs = @(
            "build"
        )
        
        if ($Debug) {
            $buildArgs += "-debug"
        }
        
        $buildArgs += @(
            "-var-file=variables/business-aws.pkrvars.hcl"
        )
        
        if ($VpcId) { $buildArgs += "-var=vpc_id=$VpcId" }
        if ($SubnetId) { $buildArgs += "-var=subnet_id=$SubnetId" }
        if ($SecurityGroupId) { $buildArgs += "-var=security_group_id=$SecurityGroupId" }
        if ($Region) { $buildArgs += "-var=aws_region=$Region" }
        if ($InstanceType) { $buildArgs += "-var=instance_type=$InstanceType" }
        if ($BusinessConfigsPath) { $buildArgs += "-var=business_configs_path=$BusinessConfigsPath" }
        
        $buildArgs += "windows-server-2022-business.pkr.hcl"
        
        Write-Host "Running: packer $($buildArgs -join ' ')" -ForegroundColor Cyan
        Write-Host "`nBuild started at: $(Get-Date)" -ForegroundColor Yellow
        Write-Host "This will take approximately 45-90 minutes..." -ForegroundColor Yellow
        
        & packer @buildArgs
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "`n✓ Packer build completed successfully!" -ForegroundColor Green
            
            # Display manifest information if available
            $manifestFile = "manifests/windows-server-2022-business-manifest.json"
            if (Test-Path $manifestFile) {
                Write-Host "`nBuild manifest:" -ForegroundColor Yellow
                $manifest = Get-Content $manifestFile -Raw | ConvertFrom-Json
                foreach ($build in $manifest.builds) {
                    Write-Host "  AMI ID: $($build.artifact_id.Split(':')[1])" -ForegroundColor Cyan
                    Write-Host "  Region: $($build.custom_data.region)" -ForegroundColor Cyan
                    Write-Host "  Build Time: $($build.custom_data.build_time)" -ForegroundColor Cyan
                }
            }
            
            return $true
        } else {
            Write-Host "`n✗ Packer build failed" -ForegroundColor Red
            return $false
        }
        
    } catch {
        Write-Error "Failed to start Packer build: $($_.Exception.Message)"
        return $false
    }
}

# Function to display usage information
function Show-Usage {
    Write-Host "`nBusiness AMI Build Summary:" -ForegroundColor Yellow
    Write-Host "This AMI includes:" -ForegroundColor White
    Write-Host "  ✓ Windows Server 2022 (latest)" -ForegroundColor Green
    Write-Host "  ✓ .NET Framework 4.8" -ForegroundColor Green
    Write-Host "  ✓ BGInfo with business configuration" -ForegroundColor Green
    Write-Host "  ✓ RSAT tools for domain operations" -ForegroundColor Green
    Write-Host "  ✓ Business-specific domain join scripts" -ForegroundColor Green
    Write-Host "  ✓ Multi-business configuration support" -ForegroundColor Green
    
    Write-Host "`nTo use the AMI:" -ForegroundColor Yellow
    Write-Host "1. Launch EC2 instance from the built AMI" -ForegroundColor White
    Write-Host "2. Provide business configuration in user data" -ForegroundColor White
    Write-Host "3. Set environment variables for domain credentials" -ForegroundColor White
    Write-Host "4. Instance will automatically join the correct domain OU" -ForegroundColor White
    
    Write-Host "`nExample launch command:" -ForegroundColor Yellow
    Write-Host "aws ec2 run-instances --image-id ami-xxxxxxxxx --instance-type t3.medium --user-data file://business1-userdata.ps1" -ForegroundColor Cyan
}

# Main execution
try {
    # Check prerequisites
    if (!(Test-Prerequisites)) {
        exit 1
    }
    
    # Validate template
    if (!(Test-PackerTemplate)) {
        exit 1
    }
    
    if ($ValidateOnly) {
        Write-Host "`n✓ Validation completed successfully!" -ForegroundColor Green
        Write-Host "Template is ready for building." -ForegroundColor Yellow
        exit 0
    }
    
    # Build AMI
    if (!(Start-PackerBuild)) {
        exit 1
    }
    
    # Show usage information
    Show-Usage
    
    Write-Host "`n✓ Business AMI build process completed successfully!" -ForegroundColor Green
    
} catch {
    Write-Error "Build process failed: $($_.Exception.Message)"
    exit 1
}
